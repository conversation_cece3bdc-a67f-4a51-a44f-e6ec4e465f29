#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面可见性测试脚本
测试软件界面是否正确显示
"""

import time
import threading
from gaokao import CountdownTimer

def test_window_visibility(app):
    """测试窗口可见性"""
    print("👁️ 测试窗口可见性...")
    
    # 检查窗口状态
    print(f"   窗口状态: {app.root.state()}")
    print(f"   窗口位置: {app.root.winfo_x()}, {app.root.winfo_y()}")
    print(f"   窗口大小: {app.root.winfo_width()}x{app.root.winfo_height()}")
    print(f"   窗口透明度: {app.root.attributes('-alpha')}")
    print(f"   窗口置顶: {app.root.attributes('-topmost')}")
    
    if app.root.state() == 'normal':
        print("   ✅ 窗口状态正常")
    else:
        print(f"   ❌ 窗口状态异常: {app.root.state()}")

def test_theme_switching(app):
    """测试主题切换可见性"""
    print("\n🎨 测试主题切换可见性...")
    
    themes = ['default', 'dark', 'colorful']
    theme_names = {'default': '默认主题', 'dark': '深色主题', 'colorful': '彩色主题'}
    
    for theme in themes:
        print(f"\n   切换到 {theme_names[theme]}...")
        app.switch_theme(theme)
        time.sleep(2)
        
        # 检查窗口是否仍然可见
        if app.root.state() == 'normal':
            print(f"   ✅ {theme_names[theme]} 窗口可见")
            if hasattr(app, 'current_theme'):
                bg_color = app.current_theme['bg']
                alpha = app.current_theme['alpha']
                print(f"   ✅ 背景色: {bg_color}, 透明度: {alpha}")
        else:
            print(f"   ❌ {theme_names[theme]} 窗口不可见")

def test_window_interaction(app):
    """测试窗口交互"""
    print("\n🖱️ 测试窗口交互...")
    
    print("   请尝试以下操作:")
    print("   • 鼠标悬停在窗口上查看工具提示")
    print("   • 右键点击打开设置面板")
    print("   • 使用快捷键 Ctrl+1/2/3 切换主题")
    print("   • 使用空格键切换目标")
    print("   • 拖拽窗口移动位置")
    
    time.sleep(5)  # 给用户时间测试

def run_visibility_test():
    """运行界面可见性测试"""
    print("👁️ 高考倒计时软件界面可见性测试")
    print("=" * 45)
    print("📋 测试项目:")
    print("  • 窗口可见性检查")
    print("  • 主题切换可见性测试")
    print("  • 窗口交互测试")
    print()
    print("💡 观察要点:")
    print("  • 窗口是否正确显示")
    print("  • 内容是否清晰可见")
    print("  • 主题切换是否正常")
    print("  • 交互功能是否工作")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_sequence():
            print("⏳ 等待应用初始化...")
            time.sleep(3)
            
            print("\n🎬 开始界面可见性测试...")
            
            # 运行各项测试
            test_window_visibility(app)
            test_theme_switching(app)
            test_window_interaction(app)
            
            # 测试总结
            print("\n📊 界面可见性测试总结")
            print("-" * 30)
            
            window_visible = app.root.state() == 'normal'
            window_positioned = app.root.winfo_x() >= 0 and app.root.winfo_y() >= 0
            window_sized = app.root.winfo_width() > 0 and app.root.winfo_height() > 0
            transparency_ok = 0.1 <= app.root.attributes('-alpha') <= 1.0
            
            features = {
                "窗口正确显示": window_visible,
                "窗口位置正常": window_positioned,
                "窗口大小正常": window_sized,
                "透明度设置正常": transparency_ok,
                "主题切换正常": hasattr(app, 'current_theme'),
            }
            
            score = sum(features.values()) / len(features) * 100
            
            print("功能完成度:")
            for feature, status in features.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {feature}")
            
            print(f"\n总体评分: {score:.0f}/100")
            
            if score >= 90:
                grade = "🏆 优秀"
                comment = "界面显示完美！"
            elif score >= 75:
                grade = "✅ 良好"
                comment = "界面显示良好"
            else:
                grade = "⚠️ 需修复"
                comment = "界面显示需要修复"
            
            print(f"等级: {grade}")
            print(f"评价: {comment}")
            
            if window_visible:
                print(f"\n✨ 界面显示特色")
                print("-" * 18)
                print("• 👁️ 窗口正确显示")
                print("• 🎨 主题切换正常")
                print("• 🔲 边框样式美观")
                print("• 💫 透明度适中")
                print("• 🖱️ 交互功能完整")
                print("• ⚡ 响应速度快")
                
                print(f"\n🎯 当前窗口信息")
                print("-" * 16)
                print(f"• 位置: ({app.root.winfo_x()}, {app.root.winfo_y()})")
                print(f"• 大小: {app.root.winfo_width()}x{app.root.winfo_height()}")
                print(f"• 透明度: {app.root.attributes('-alpha')}")
                print(f"• 置顶: {app.root.attributes('-topmost')}")
                
                print(f"\n✅ 界面可见性测试通过！")
                print("软件界面正确显示，所有功能正常工作")
            else:
                print(f"\n❌ 界面可见性测试失败！")
                print("软件界面无法正确显示，需要进一步调试")
            
            print(f"\n🎮 现在您可以:")
            print("  • 观察软件界面是否正确显示")
            print("  • 尝试各种交互功能")
            print("  • 测试主题切换效果")
            print("  • 验证所有功能是否正常")
            
            print(f"\n🎊 测试完成！软件将继续运行...")
            print("按 Ctrl+Q 或关闭窗口退出")
            
        # 启动测试
        test_thread = threading.Thread(target=test_sequence, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_visibility_test()
