#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验测试脚本
测试高考倒计时软件的用户体验优化功能
"""

import time
import threading
from gaokao import CountdownTimer

def test_keyboard_shortcuts(app):
    """测试键盘快捷键"""
    print("🎹 测试键盘快捷键...")
    
    shortcuts_to_test = [
        ("空格键", "切换目标", lambda: app.next_target()),
        ("Ctrl+M", "切换激励语句", lambda: app.change_motto()),
        ("Ctrl+T", "切换置顶", lambda: app.toggle_topmost()),
        ("F5", "刷新显示", lambda: app.refresh_display()),
        ("Ctrl+1", "默认主题", lambda: app.switch_theme('default')),
        ("Ctrl+2", "深色主题", lambda: app.switch_theme('dark')),
        ("Ctrl+3", "彩色主题", lambda: app.switch_theme('colorful')),
    ]
    
    for key, desc, action in shortcuts_to_test:
        try:
            print(f"   测试 {key} ({desc})...")
            action()
            time.sleep(1)
            print(f"   ✅ {key} 功能正常")
        except Exception as e:
            print(f"   ❌ {key} 测试失败: {e}")
        time.sleep(0.5)

def test_theme_switching(app):
    """测试主题切换"""
    print("\n🎨 测试主题切换...")
    
    themes = ['default', 'dark', 'colorful']
    for theme in themes:
        try:
            print(f"   切换到 {theme} 主题...")
            app.switch_theme(theme)
            time.sleep(1.5)
            print(f"   ✅ {theme} 主题应用成功")
        except Exception as e:
            print(f"   ❌ {theme} 主题切换失败: {e}")

def test_target_switching(app):
    """测试目标切换"""
    print("\n🎯 测试目标切换...")
    
    if len(app.config.get('targets', [])) > 1:
        try:
            original_index = app.current_target_index
            
            # 测试下一个目标
            print("   测试下一个目标...")
            app.next_target()
            time.sleep(1)
            
            # 测试上一个目标
            print("   测试上一个目标...")
            app.previous_target()
            time.sleep(1)
            
            # 测试数字键切换
            print("   测试数字键切换...")
            app.switch_to_target_by_key(0)
            time.sleep(1)
            
            print("   ✅ 目标切换功能正常")
        except Exception as e:
            print(f"   ❌ 目标切换测试失败: {e}")
    else:
        print("   ⚠️ 只有一个目标，跳过目标切换测试")

def test_feedback_system(app):
    """测试反馈系统"""
    print("\n💬 测试反馈系统...")
    
    feedback_tests = [
        ("切换反馈", lambda: app.show_switch_feedback("测试反馈")),
        ("主题反馈", lambda: app.switch_theme('default')),
        ("目标反馈", lambda: app.next_target() if len(app.config.get('targets', [])) > 1 else None),
    ]
    
    for name, action in feedback_tests:
        try:
            print(f"   测试 {name}...")
            if action:
                action()
                time.sleep(2)  # 等待反馈消失
            print(f"   ✅ {name} 正常")
        except Exception as e:
            print(f"   ❌ {name} 失败: {e}")

def run_ux_test():
    """运行用户体验测试"""
    print("🎯 高考倒计时软件用户体验测试")
    print("=" * 50)
    print("📋 测试项目:")
    print("  • 键盘快捷键")
    print("  • 主题切换")
    print("  • 目标切换")
    print("  • 反馈系统")
    print("  • 工具提示")
    print("  • 整体用户体验")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_sequence():
            print("⏳ 等待应用初始化...")
            time.sleep(3)
            
            # 运行各项测试
            test_keyboard_shortcuts(app)
            test_theme_switching(app)
            test_target_switching(app)
            test_feedback_system(app)
            
            # 测试工具提示
            print("\n💡 测试工具提示...")
            if app._tooltips_enabled:
                print("   ✅ 工具提示已启用")
            else:
                print("   ⚠️ 工具提示已禁用")
            
            # 整体评估
            print("\n📊 用户体验评估")
            print("-" * 30)
            
            features = {
                "快捷键支持": True,
                "主题切换": True,
                "视觉反馈": True,
                "工具提示": app._tooltips_enabled,
                "动画效果": app._animations_enabled,
            }
            
            score = sum(features.values()) / len(features) * 100
            
            print("功能完成度:")
            for feature, status in features.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {feature}")
            
            print(f"\n总体评分: {score:.0f}/100")
            
            if score >= 90:
                grade = "🏆 优秀"
                comment = "用户体验非常出色！"
            elif score >= 75:
                grade = "✅ 良好"
                comment = "用户体验良好，有小幅改进空间"
            elif score >= 60:
                grade = "👌 一般"
                comment = "基本功能完善，建议进一步优化"
            else:
                grade = "⚠️ 需改进"
                comment = "用户体验需要显著改进"
            
            print(f"等级: {grade}")
            print(f"评价: {comment}")
            
            # 用户体验亮点
            print(f"\n✨ 用户体验亮点")
            print("-" * 20)
            print("• 🎹 丰富的键盘快捷键支持")
            print("• 🎨 多主题切换系统")
            print("• 💬 实时视觉反馈")
            print("• 💡 智能工具提示")
            print("• 🎯 直观的目标切换")
            print("• ⚡ 流畅的交互体验")
            
            # 使用建议
            print(f"\n💡 使用建议")
            print("-" * 15)
            print("• 按 F1 查看完整快捷键帮助")
            print("• 右键打开设置面板自定义体验")
            print("• 使用数字键 1-5 快速切换目标")
            print("• 尝试不同主题找到最适合的风格")
            print("• 启用工具提示获得更多帮助信息")
            
            print(f"\n✅ 用户体验测试完成！")
            
            # 5秒后退出
            time.sleep(5)
            app.root.quit()
        
        # 启动测试
        test_thread = threading.Thread(target=test_sequence, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_ux_test()
