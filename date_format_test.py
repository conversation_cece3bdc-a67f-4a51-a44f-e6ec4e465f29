#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日期格式测试脚本
验证日期显示格式修改效果
"""

import time
import threading
from gaokao import CountdownTimer

def test_date_format():
    """测试日期格式显示"""
    print("📅 日期格式显示测试")
    print("=" * 40)
    print("📋 测试目标:")
    print("  ✓ 验证日期格式为'年月日 星期几'")
    print("  ✓ 检查不同日期的显示效果")
    print("  ✓ 确认格式美观性和可读性")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_logic():
            print("⏳ 应用初始化中...")
            time.sleep(2)
            
            # 获取当前显示的日期文本
            current_date_text = app.date_label.cget('text')
            print(f"📝 当前日期显示: {current_date_text}")
            
            # 分析日期格式
            print(f"\n📊 日期格式分析:")
            
            if "年" in current_date_text and "月" in current_date_text and "日" in current_date_text:
                print("   ✅ 包含中文年月日格式")
            else:
                print("   ❌ 缺少中文年月日格式")
            
            if "星期" in current_date_text:
                print("   ✅ 包含星期信息")
            else:
                print("   ❌ 缺少星期信息")
            
            # 检查格式示例
            print(f"\n📋 格式对比:")
            print(f"   修改前格式: 2024-06-07 星期五")
            print(f"   修改后格式: 2024年6月7日 星期五")
            print(f"   实际显示: {current_date_text}")
            
            # 评估可读性
            if ("年" in current_date_text and "月" in current_date_text and 
                "日" in current_date_text and "星期" in current_date_text):
                print(f"\n✅ 格式修改成功！")
                print(f"   优点:")
                print(f"   • 使用中文年月日，更符合中文阅读习惯")
                print(f"   • 保留星期信息，便于用户了解")
                print(f"   • 格式清晰易读，视觉效果更好")
                
                grade = "🏆 优秀"
                desc = "日期格式完美符合中文习惯"
            else:
                print(f"\n⚠️ 格式可能需要调整")
                grade = "👍 需要检查"
                desc = "请确认日期格式是否正确"
            
            print(f"\n🏆 格式评价")
            print("=" * 30)
            print(f"评级: {grade}")
            print(f"评价: {desc}")
            
            # 测试不同的目标（如果有多个）
            if len(app.config.get('targets', [])) > 1:
                print(f"\n📊 多目标日期格式测试:")
                for i, target in enumerate(app.config['targets']):
                    print(f"   目标{i+1}: {target['name']} - {target['date']}")
                    
                    # 切换到不同目标查看日期格式
                    if i < 3:  # 最多测试3个目标
                        app.switch_to_target(i)
                        time.sleep(0.5)
                        target_date_text = app.date_label.cget('text')
                        print(f"   显示: {target_date_text}")
            
            # 显示格式规范
            print(f"\n📐 日期格式规范:")
            print(f"   标准格式: YYYY年MM月DD日 星期X")
            print(f"   示例: 2024年6月7日 星期五")
            print(f"   特点: 中文数字格式，易读性强")
            
            print(f"\n💡 用户体验提升:")
            print(f"   • 更符合中文用户的阅读习惯")
            print(f"   • 视觉上更加美观和统一")
            print(f"   • 信息层次更加清晰")
            
            print(f"\n✅ 测试完成！")
            
            # 等待用户查看效果
            time.sleep(3)
            app.root.quit()
        
        # 启动测试
        test_thread = threading.Thread(target=test_logic, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

def show_format_comparison():
    """显示格式对比"""
    print("📊 日期格式对比:")
    print("-" * 30)
    print("修改前: 2024-06-07 星期五")
    print("修改后: 2024年6月7日 星期五")
    print()
    print("优势:")
    print("✓ 更符合中文阅读习惯")
    print("✓ 视觉效果更美观")
    print("✓ 信息表达更清晰")
    print()

if __name__ == "__main__":
    show_format_comparison()
    test_date_format()
