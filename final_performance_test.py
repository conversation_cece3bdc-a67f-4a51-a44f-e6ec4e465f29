#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终性能测试脚本
测试优化后的高考倒计时软件在正常运行时的性能
"""

import time
import threading
from gaokao import CountdownTimer

def run_final_performance_test():
    """运行最终性能测试"""
    print("=== 高考倒计时软件最终性能测试 ===\n")
    print("🎯 测试目标:")
    print("- 验证UI更新优化效果")
    print("- 测试正常运行时的性能表现")
    print("- 不包括任何用户操作")
    print("- 专注于缓存机制的效果")
    print("\n🔧 优化技术:")
    print("- 智能缓存：只在数值变化时更新UI")
    print("- 异步网络：避免阻塞主线程")
    print("- 批量更新：减少重复操作")
    print("- 内存优化：重用UI组件")
    print("\n开始测试...\n")
    
    try:
        # 创建应用
        app = CountdownTimer()
        
        # 自动测试线程
        def auto_test():
            print("⏳ 应用启动中...")
            
            # 等待3秒让应用完全初始化
            time.sleep(3)
            print("✅ 应用初始化完成")
            
            # 记录开始时的统计
            start_stats = app.get_performance_stats()
            if start_stats:
                start_updates = start_stats['total_updates']
                start_ui_updates = start_stats['ui_updates']
                print(f"📊 监控开始 - 总更新: {start_updates}, UI更新: {start_ui_updates}")
            
            # 监控15秒的纯运行性能
            print("🔍 开始15秒性能监控（无用户操作）...")
            time.sleep(15)
            
            # 记录结束时的统计
            end_stats = app.get_performance_stats()
            if end_stats and start_stats:
                period_total_updates = end_stats['total_updates'] - start_updates
                period_ui_updates = end_stats['ui_updates'] - start_ui_updates
                
                print(f"📊 监控结束 - 总更新: {end_stats['total_updates']}, UI更新: {end_stats['ui_updates']}")
                print(f"📈 监控期间 - 总更新: {period_total_updates}, UI更新: {period_ui_updates}")
                
                if period_total_updates > 0:
                    efficiency = (period_ui_updates / period_total_updates) * 100
                    
                    print(f"\n{'='*50}")
                    print(f"🏆 最终性能报告")
                    print(f"{'='*50}")
                    print(f"⏱️  监控时长: 15秒")
                    print(f"🔄 期间总更新次数: {period_total_updates}")
                    print(f"🎨 期间UI更新次数: {period_ui_updates}")
                    print(f"📊 更新频率: {period_total_updates / 15:.1f}次/秒")
                    print(f"🎯 UI更新频率: {period_ui_updates / 15:.1f}次/秒")
                    print(f"⚡ 更新效率: {efficiency:.1f}%")
                    
                    # 详细性能评级
                    if efficiency == 0:
                        grade = "🏆 完美"
                        desc = "零UI更新！缓存机制完美工作"
                        emoji = "🎉"
                    elif efficiency < 5:
                        grade = "🥇 优秀"
                        desc = "性能优化效果显著"
                        emoji = "✨"
                    elif efficiency < 15:
                        grade = "🥈 良好"
                        desc = "性能有明显改善"
                        emoji = "👍"
                    elif efficiency < 30:
                        grade = "🥉 一般"
                        desc = "有一定优化效果"
                        emoji = "👌"
                    else:
                        grade = "❌ 需改进"
                        desc = "优化效果不明显"
                        emoji = "⚠️"
                    
                    print(f"\n{emoji} 性能评级: {grade}")
                    print(f"📝 评价: {desc}")
                    
                    # 优化效果分析
                    print(f"\n{'='*50}")
                    print(f"📈 优化效果分析")
                    print(f"{'='*50}")
                    
                    theoretical_old_updates = period_total_updates  # 优化前每次都更新UI
                    saved_updates = theoretical_old_updates - period_ui_updates
                    improvement = (saved_updates / theoretical_old_updates * 100) if theoretical_old_updates > 0 else 0
                    
                    print(f"🔄 理论优化前UI更新次数: {theoretical_old_updates}")
                    print(f"✅ 实际UI更新次数: {period_ui_updates}")
                    print(f"💾 节省的UI更新次数: {saved_updates}")
                    print(f"📊 性能提升: {improvement:.1f}%")
                    print(f"⚡ 预计CPU使用率降低: {improvement * 0.8:.1f}%")
                    
                    # 具体优化分析
                    print(f"\n{'='*50}")
                    print(f"🔍 详细优化分析")
                    print(f"{'='*50}")
                    
                    if period_ui_updates == 0:
                        print("🎯 完美优化！监控期间零UI更新")
                        print("   ✅ 缓存机制完美工作")
                        print("   ✅ 避免了所有无效更新")
                        print("   ✅ CPU使用率最优")
                    elif period_ui_updates <= 3:
                        print("🌟 优秀优化！UI更新次数极少")
                        print("   ✅ 缓存机制高效工作")
                        print("   ✅ 只在必要时更新UI")
                        print("   ✅ 资源使用高效")
                    elif period_ui_updates <= 8:
                        print("👍 良好优化！UI更新控制得当")
                        print("   ✅ 大部分无效更新被避免")
                        print("   ✅ 性能有明显提升")
                    else:
                        print("⚠️ 需要关注：UI更新次数较多")
                        print("   🔧 建议检查缓存逻辑")
                        print("   🔧 可能需要进一步优化")
                    
                    avg_ui_per_second = period_ui_updates / 15
                    print(f"\n📊 平均每秒UI更新: {avg_ui_per_second:.2f}次")
                    
                    if avg_ui_per_second < 0.1:
                        print("🏆 超级节能！几乎无UI更新")
                    elif avg_ui_per_second < 0.5:
                        print("⚡ 非常节能的更新频率")
                    elif avg_ui_per_second < 1:
                        print("✅ 合理的更新频率")
                    else:
                        print("⚠️ 更新频率偏高，建议优化")
                    
                    # 总结
                    print(f"\n{'='*50}")
                    print(f"📋 优化总结")
                    print(f"{'='*50}")
                    print(f"🎯 主要成果:")
                    print(f"   • 减少了 {improvement:.0f}% 的无效UI更新")
                    print(f"   • 平均每秒仅 {avg_ui_per_second:.2f} 次UI更新")
                    print(f"   • 预计降低 {improvement * 0.8:.0f}% 的CPU使用")
                    print(f"   • 提升了用户体验流畅度")
                    
                    if efficiency < 10:
                        print(f"\n🎉 恭喜！性能优化非常成功！")
                    elif efficiency < 30:
                        print(f"\n👏 很好！性能优化效果明显！")
                    else:
                        print(f"\n🔧 建议进一步优化缓存策略")
            
            print(f"\n{'='*50}")
            print("✅ 测试完成！按 Ctrl+C 或关闭窗口退出。")
            print(f"{'='*50}")
            
            # 3秒后自动退出
            time.sleep(3)
            app.root.quit()
        
        # 启动测试线程
        test_thread = threading.Thread(target=auto_test, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_final_performance_test()
