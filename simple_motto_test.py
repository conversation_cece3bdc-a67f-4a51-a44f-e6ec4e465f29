#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的激励语句切换测试
"""

import time
import threading
from gaokao import CountdownTimer

def simple_motto_test():
    """简化的激励语句切换测试"""
    print("🎯 激励语句切换优化测试")
    print("=" * 40)
    print("📋 测试目标:")
    print("  ✓ 验证在线切换时的加载提示")
    print("  ✓ 验证网络失败时的降级处理")
    print("  ✓ 验证用户体验改进")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_logic():
            print("⏳ 应用初始化中...")
            time.sleep(2)
            
            current_motto = app.current_motto
            print(f"📝 初始激励语句: {current_motto[:50]}...")
            
            # 测试1：本地激励语句切换
            print(f"\n📊 测试1: 本地激励语句切换")
            app.config['use_api'] = False
            
            print("   执行本地切换...")
            app.change_motto()
            time.sleep(0.5)
            
            local_motto = app.current_motto
            print(f"   本地切换结果: {local_motto[:50]}...")
            
            if local_motto != current_motto:
                print("   ✅ 本地切换成功")
            else:
                print("   ⚠️ 本地切换结果相同（可能随机到相同语句）")
            
            # 测试2：在线激励语句切换
            print(f"\n📊 测试2: 在线激励语句切换")
            app.config['use_api'] = True
            
            print("   执行在线切换...")
            before_online = app.current_motto
            app.change_motto()
            
            # 立即检查是否显示加载提示
            immediate_result = app.current_motto
            print(f"   切换瞬间显示: {immediate_result}")
            
            if "正在获取" in immediate_result:
                print("   ✅ 显示加载提示 - 用户体验优化成功！")
            else:
                print("   ⚠️ 未显示加载提示")
            
            # 等待异步请求完成
            print("   等待在线激励语句加载...")
            for i in range(5):
                time.sleep(1)
                current = app.current_motto
                if "正在获取" not in current:
                    print(f"   第{i+1}秒: 加载完成")
                    break
                else:
                    print(f"   第{i+1}秒: 仍在加载...")
            
            final_online = app.current_motto
            print(f"   最终在线结果: {final_online[:50]}...")
            
            if "正在获取" not in final_online:
                if final_online != before_online:
                    print("   ✅ 在线切换成功")
                else:
                    print("   ⚠️ 在线切换结果相同")
            else:
                print("   ❌ 在线切换失败，仍显示加载状态")
            
            # 测试3：对比优化前后的体验
            print(f"\n📊 测试3: 用户体验对比")
            print("   优化前的问题:")
            print("   ❌ 先显示本地语句，然后跳转到在线语句")
            print("   ❌ 用户不知道是否在加载")
            print("   ❌ 体验不连贯")
            print()
            print("   优化后的改进:")
            print("   ✅ 显示'正在获取激励语句...'加载提示")
            print("   ✅ 用户明确知道当前状态")
            print("   ✅ 网络失败时自动降级到本地语句")
            print("   ✅ 体验更加流畅和可预期")
            
            # 最终验证
            print(f"\n🏆 优化验证结果")
            print("=" * 40)
            
            # 再次测试在线切换以确认优化效果
            print("   最终验证在线切换...")
            app.change_motto()
            verification_result = app.current_motto
            
            if "正在获取" in verification_result:
                print("   ✅ 优化成功！切换时正确显示加载提示")
                grade = "🏆 优秀"
                desc = "用户体验优化非常成功"
            else:
                print("   ⚠️ 优化部分成功，但加载提示可能有问题")
                grade = "👍 良好"
                desc = "基本功能正常，但体验可以进一步改进"
            
            print(f"   优化等级: {grade}")
            print(f"   评价: {desc}")
            
            print(f"\n💡 使用建议:")
            print("   • 启用在线激励语句可获得更丰富的内容")
            print("   • 加载提示让用户了解当前状态")
            print("   • 网络问题时会自动使用本地语句")
            print("   • 整体体验更加流畅和可预期")
            
            print(f"\n✅ 测试完成！")
            
            # 等待一下让用户看到最终结果
            time.sleep(3)
            app.root.quit()
        
        # 启动测试
        test_thread = threading.Thread(target=test_logic, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_motto_test()
