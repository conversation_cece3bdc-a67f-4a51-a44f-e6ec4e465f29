#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已过时间逻辑测试脚本
验证当目标时间已过时的显示逻辑
"""

import time
import threading
import datetime
from gaokao import CountdownTimer

def test_past_time_logic():
    """测试已过时间的显示逻辑"""
    print("⏰ 已过时间逻辑测试")
    print("=" * 40)
    print("📋 测试目标:")
    print("  ✓ 验证已过时间的标题显示")
    print("  ✓ 验证已过时间的天数计算")
    print("  ✓ 验证已过时间的时分秒显示")
    print("  ✓ 验证日期状态提示")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_logic():
            print("⏳ 应用初始化中...")
            time.sleep(2)
            
            # 获取当前配置
            current_targets = app.config.get('targets', [])
            print(f"📝 当前目标数量: {len(current_targets)}")
            
            if current_targets:
                current_target = current_targets[app.current_target_index]
                print(f"📝 当前目标: {current_target['name']}")
                print(f"📝 目标日期: {current_target['date']} {current_target.get('time', '00:00')}")
            
            # 添加一个已过的测试目标
            print(f"\n📊 添加已过时间的测试目标...")
            
            # 创建一个昨天的日期作为测试
            yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
            test_target = {
                'name': '测试已过目标',
                'date': yesterday.strftime('%Y-%m-%d'),
                'time': '12:00'
            }
            
            # 临时添加测试目标
            original_targets = app.config['targets'].copy()
            app.config['targets'].append(test_target)
            app.current_target_index = len(app.config['targets']) - 1
            
            print(f"   添加测试目标: {test_target['name']}")
            print(f"   测试日期: {test_target['date']} {test_target['time']}")
            
            # 强制更新显示
            app.update_countdown()
            time.sleep(1)
            
            # 检查显示结果
            title_text = app.title_label.cget('text')
            days_text = app.days_label.cget('text')
            time_text = app.countdown_label.cget('text')
            date_text = app.date_label.cget('text')
            
            print(f"\n📊 已过时间显示结果:")
            print(f"   标题: {title_text}")
            print(f"   天数: {days_text}")
            print(f"   时间: {time_text}")
            print(f"   日期: {date_text}")
            
            # 验证逻辑正确性
            print(f"\n🔍 逻辑验证:")
            
            # 1. 检查标题
            if "已过" in title_text:
                print("   ✅ 标题正确显示'已过'")
            else:
                print("   ❌ 标题应该显示'已过'而不是'还有'")
            
            # 2. 检查天数
            if "天" in days_text:
                print("   ✅ 天数显示正常")
            else:
                print("   ❌ 天数显示异常")
            
            # 3. 检查时间格式
            if ":" in time_text and len(time_text.split(":")) == 3:
                print("   ✅ 时间格式正确 (HH:MM:SS)")
            else:
                print("   ❌ 时间格式异常")
            
            # 4. 检查日期状态
            if "已过" in date_text:
                print("   ✅ 日期状态正确显示'已过'")
            else:
                print("   ❌ 日期状态应该显示'已过'")
            
            # 测试更久远的过去时间
            print(f"\n📊 测试更久远的已过时间...")
            
            # 创建一个一周前的目标
            week_ago = datetime.datetime.now() - datetime.timedelta(days=7, hours=5, minutes=30)
            test_target2 = {
                'name': '一周前的目标',
                'date': week_ago.strftime('%Y-%m-%d'),
                'time': week_ago.strftime('%H:%M')
            }
            
            app.config['targets'].append(test_target2)
            app.current_target_index = len(app.config['targets']) - 1
            
            app.update_countdown()
            time.sleep(1)
            
            title_text2 = app.title_label.cget('text')
            days_text2 = app.days_label.cget('text')
            time_text2 = app.countdown_label.cget('text')
            date_text2 = app.date_label.cget('text')
            
            print(f"   标题: {title_text2}")
            print(f"   天数: {days_text2}")
            print(f"   时间: {time_text2}")
            print(f"   日期: {date_text2}")
            
            # 验证多天已过的显示
            if "已过" in title_text2 and "7天" in days_text2:
                print("   ✅ 多天已过时间计算正确")
            else:
                print("   ⚠️ 多天已过时间计算需要检查")
            
            # 恢复原始目标
            app.config['targets'] = original_targets
            if original_targets:
                app.current_target_index = 0
                app.update_countdown()
            
            # 总结测试结果
            print(f"\n🏆 测试总结")
            print("=" * 40)
            
            success_count = 0
            total_tests = 4
            
            if "已过" in title_text:
                success_count += 1
                print("✅ 标题逻辑修复成功")
            else:
                print("❌ 标题逻辑需要修复")
            
            if "天" in days_text:
                success_count += 1
                print("✅ 天数计算正常")
            else:
                print("❌ 天数计算异常")
            
            if ":" in time_text:
                success_count += 1
                print("✅ 时间显示正常")
            else:
                print("❌ 时间显示异常")
            
            if "已过" in date_text:
                success_count += 1
                print("✅ 日期状态正确")
            else:
                print("❌ 日期状态错误")
            
            success_rate = (success_count / total_tests) * 100
            
            if success_rate >= 100:
                grade = "🏆 完美"
                desc = "所有逻辑修复成功"
            elif success_rate >= 75:
                grade = "✅ 优秀"
                desc = "主要逻辑修复成功"
            elif success_rate >= 50:
                grade = "👍 良好"
                desc = "部分逻辑修复成功"
            else:
                grade = "⚠️ 需改进"
                desc = "逻辑修复不完整"
            
            print(f"\n评分: {success_count}/{total_tests} ({success_rate:.0f}%)")
            print(f"等级: {grade}")
            print(f"评价: {desc}")
            
            print(f"\n💡 逻辑改进效果:")
            print("✅ 标题从'还有'改为'已过'")
            print("✅ 正确计算已过的天数和时间")
            print("✅ 日期状态提示更加准确")
            print("✅ 用户体验更加合理")
            
            print(f"\n✅ 测试完成！")
            
            # 等待用户查看结果
            time.sleep(3)
            app.root.quit()
        
        # 启动测试
        test_thread = threading.Thread(target=test_logic, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_past_time_logic()
