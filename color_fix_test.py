#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色修复验证测试
验证切换目标时颜色正确更新
"""

import time
import threading
import datetime
from gaokao import CountdownTimer

def test_color_fix():
    """测试颜色修复效果"""
    print("🔧 颜色修复验证测试")
    print("=" * 40)
    print("📋 测试目标:")
    print("  ✓ 验证切换目标时颜色正确更新")
    print("  ✓ 确认缓存重置机制工作正常")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_logic():
            print("⏳ 应用初始化中...")
            time.sleep(2)
            
            # 创建测试目标
            future_date = datetime.datetime.now() + datetime.timedelta(days=10)
            past_date = datetime.datetime.now() - datetime.timedelta(days=3)
            
            test_targets = [
                {
                    'name': '未来目标',
                    'date': future_date.strftime('%Y-%m-%d'),
                    'time': '12:00'
                },
                {
                    'name': '已过目标',
                    'date': past_date.strftime('%Y-%m-%d'),
                    'time': '15:30'
                }
            ]
            
            # 保存原始设置
            original_targets = app.config['targets'].copy()
            original_index = app.current_target_index
            
            # 设置测试目标
            app.config['targets'] = test_targets
            
            print(f"\n📊 颜色切换测试:")
            
            results = []
            
            for i, target in enumerate(test_targets):
                print(f"\n   切换到目标{i+1}: {target['name']}")
                
                # 切换目标
                app.switch_to_target(i)
                time.sleep(1)  # 等待更新完成
                
                # 获取显示信息
                title = app.title_label.cget('text')
                days_text = app.days_label.cget('text')
                days_color = app.days_label.cget('fg')
                
                print(f"   标题: {title}")
                print(f"   天数: {days_text}")
                print(f"   颜色: {days_color}")
                
                # 判断颜色是否正确
                expected_color = '#FF4500' if i == 0 else '#808080'
                is_correct = days_color.upper() == expected_color.upper()
                
                if is_correct:
                    print(f"   ✅ 颜色正确 ({expected_color})")
                else:
                    print(f"   ❌ 颜色错误，期望 {expected_color}，实际 {days_color}")
                
                results.append({
                    'target': target['name'],
                    'expected': expected_color,
                    'actual': days_color,
                    'correct': is_correct
                })
            
            # 多次切换测试
            print(f"\n📊 多次切换测试:")
            
            for round_num in range(3):
                print(f"\n   第{round_num + 1}轮切换:")
                
                for i in [0, 1, 0]:  # 未来 -> 已过 -> 未来
                    app.switch_to_target(i)
                    time.sleep(0.5)
                    
                    color = app.days_label.cget('fg')
                    target_name = test_targets[i]['name']
                    expected = '#FF4500' if i == 0 else '#808080'
                    
                    status = "✅" if color.upper() == expected.upper() else "❌"
                    print(f"     {target_name}: {color} {status}")
            
            # 恢复原始设置
            app.config['targets'] = original_targets
            app.current_target_index = original_index
            app.switch_to_target(original_index)
            
            # 统计结果
            correct_count = sum(1 for r in results if r['correct'])
            total_count = len(results)
            success_rate = (correct_count / total_count) * 100
            
            print(f"\n🏆 测试结果总结")
            print("=" * 40)
            
            for result in results:
                status = "✅" if result['correct'] else "❌"
                print(f"{status} {result['target']}: 期望 {result['expected']}, 实际 {result['actual']}")
            
            print(f"\n成功率: {correct_count}/{total_count} ({success_rate:.0f}%)")
            
            if success_rate >= 100:
                grade = "🏆 完美"
                desc = "颜色切换完全正确"
            elif success_rate >= 50:
                grade = "✅ 良好"
                desc = "颜色切换基本正确"
            else:
                grade = "⚠️ 需改进"
                desc = "颜色切换需要修复"
            
            print(f"等级: {grade}")
            print(f"评价: {desc}")
            
            print(f"\n🎨 颜色方案:")
            print("• 未来目标: #FF4500 (橙红色) - 醒目提醒")
            print("• 已过目标: #808080 (灰色) - 低调显示")
            
            print(f"\n💡 修复效果:")
            if success_rate >= 100:
                print("✅ 切换目标时缓存正确重置")
                print("✅ 颜色实时更新正常")
                print("✅ 视觉反馈准确无误")
            else:
                print("⚠️ 可能需要进一步调试缓存机制")
            
            print(f"\n✅ 测试完成！")
            
            # 等待用户查看
            time.sleep(3)
            app.root.quit()
        
        # 启动测试
        test_thread = threading.Thread(target=test_logic, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_color_fix()
