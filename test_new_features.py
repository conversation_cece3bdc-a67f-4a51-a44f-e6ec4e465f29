#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新功能测试脚本
测试快捷键支持和声音提醒功能
"""

import time
import threading
from gaokao import CountdownTimer

def test_new_features():
    """测试新功能"""
    print("🎯 高考倒计时软件新功能测试")
    print("=" * 50)
    print("🆕 新增功能:")
    print("  ✓ 快捷键支持")
    print("    • Ctrl+S : 打开设置面板")
    print("    • Space  : 切换目标")
    print("    • Ctrl+M : 切换激励语句")
    print("  ✓ 声音提醒")
    print("    • 整点报时")
    print("    • 重要节点提醒")
    print("    • 自定义提醒音")
    print()
    print("📋 测试说明:")
    print("1. 应用启动后，请尝试使用快捷键")
    print("2. 在设置面板中配置声音提醒")
    print("3. 测试声音播放功能")
    print("4. 观察整点报时和重要节点提醒")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_logic():
            print("⏳ 应用启动完成")
            print("🎹 快捷键测试:")
            print("   请尝试以下快捷键:")
            print("   • 按 Ctrl+S 打开设置面板")
            print("   • 按 Space 切换目标（如果有多个目标）")
            print("   • 按 Ctrl+M 切换激励语句")
            print()
            
            time.sleep(3)
            
            print("🔊 声音功能测试:")
            print("   1. 检查声音提醒配置...")
            
            # 检查声音配置
            sound_enabled = app.config.get('sound_enabled', True)
            hourly_chime = app.config.get('hourly_chime', True)
            important_reminders = app.config.get('important_reminders', True)
            
            print(f"   • 声音提醒: {'✅ 启用' if sound_enabled else '❌ 禁用'}")
            print(f"   • 整点报时: {'✅ 启用' if hourly_chime else '❌ 禁用'}")
            print(f"   • 重要节点提醒: {'✅ 启用' if important_reminders else '❌ 禁用'}")
            
            if app._custom_sound_path:
                print(f"   • 自定义声音: {app._custom_sound_path}")
            else:
                print("   • 自定义声音: 使用系统默认")
            
            print()
            print("   2. 测试声音播放...")
            
            if sound_enabled:
                print("   播放测试音...")
                app.play_sound("milestone")
                time.sleep(2)
                
                print("   播放整点报时音...")
                app.play_sound("hourly")
                time.sleep(2)
            else:
                print("   声音提醒已禁用，跳过声音测试")
            
            print()
            print("🎯 重要节点提醒测试:")
            
            # 检查当前目标的剩余天数
            if app.config['targets']:
                target = app.config['targets'][app.current_target_index]
                target_name = target['name']
                target_date = target['date']
                
                import datetime
                target_datetime = datetime.datetime.strptime(target_date, '%Y-%m-%d')
                now = datetime.datetime.now()
                days_remaining = (target_datetime - now).days
                
                print(f"   当前目标: {target_name}")
                print(f"   剩余天数: {days_remaining}")
                
                # 检查是否接近重要节点
                milestones = app._important_milestones
                next_milestone = None
                for milestone in sorted(milestones, reverse=True):
                    if days_remaining >= milestone:
                        next_milestone = milestone
                        break
                
                if next_milestone:
                    print(f"   下一个重要节点: {next_milestone} 天")
                    if days_remaining == next_milestone:
                        print("   🎉 正好到达重要节点！")
                else:
                    print("   已过所有重要节点")
            
            print()
            print("⌨️ 快捷键功能验证:")
            print("   请手动测试以下功能:")
            print("   1. 按 Ctrl+S 应该打开设置面板")
            print("   2. 在设置面板中配置声音选项")
            print("   3. 点击'测试声音'按钮验证声音播放")
            print("   4. 按 Space 切换目标（如果有多个）")
            print("   5. 按 Ctrl+M 切换激励语句")
            
            print()
            print("🔧 配置建议:")
            print("   • 在设置面板中启用所需的声音功能")
            print("   • 选择自定义提醒音文件（可选）")
            print("   • 设置多个倒计时目标测试切换功能")
            print("   • 调整重要节点提醒的天数设置")
            
            print()
            print("✅ 新功能测试完成！")
            print("   应用将继续运行，您可以继续测试各项功能")
            print("   按 Ctrl+C 或关闭窗口退出")
        
        # 启动测试
        test_thread = threading.Thread(target=test_logic, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

def show_feature_details():
    """显示新功能详细说明"""
    print("📖 新功能详细说明:")
    print()
    print("🎹 快捷键支持:")
    print("   • Ctrl+S : 快速打开设置面板")
    print("   • Space  : 在多个目标间快速切换")
    print("   • Ctrl+M : 快速切换激励语句")
    print("   • 支持大小写，确保兼容性")
    print("   • 窗口获得焦点时自动激活快捷键")
    print()
    print("🔊 声音提醒功能:")
    print("   • 整点报时: 每小时整点播放提醒音")
    print("   • 重要节点提醒: 在关键天数播放特殊提醒")
    print("   • 自定义提醒音: 支持 WAV、MP3 等格式")
    print("   • 智能提醒: 避免重复提醒同一节点")
    print("   • 异步播放: 不阻塞主界面")
    print()
    print("⚙️ 配置选项:")
    print("   • 可独立开关各项声音功能")
    print("   • 支持自定义重要节点天数")
    print("   • 配置自动保存到文件")
    print("   • 提供声音测试功能")
    print()

if __name__ == "__main__":
    show_feature_details()
    test_new_features()
