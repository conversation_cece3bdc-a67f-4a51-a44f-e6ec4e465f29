#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版性能测试脚本
不依赖外部库，仅测试应用内部性能指标
"""

import time
import threading
import gc
from gaokao import CountdownTimer

class SimplePerformanceMonitor:
    def __init__(self):
        self.start_time = time.time()
        self.memory_checks = []
        self.monitoring = False
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_loop(self):
        """简单的监控循环"""
        while self.monitoring:
            try:
                # 强制垃圾回收并记录对象数量
                gc.collect()
                obj_count = len(gc.get_objects())
                self.memory_checks.append(obj_count)
                time.sleep(2)  # 每2秒检查一次
            except Exception as e:
                print(f"监控出错: {e}")
                break
                
    def get_stats(self):
        """获取统计信息"""
        if not self.memory_checks:
            return None
            
        runtime = time.time() - self.start_time
        
        return {
            'runtime': runtime,
            'object_count_avg': sum(self.memory_checks) / len(self.memory_checks),
            'object_count_max': max(self.memory_checks),
            'object_count_min': min(self.memory_checks),
            'checks': len(self.memory_checks),
            'memory_stable': max(self.memory_checks) - min(self.memory_checks) < 100
        }
        
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        if stats:
            print("\n=== 简化性能统计 ===")
            print(f"运行时间: {stats['runtime']:.1f}秒")
            print(f"对象数量 - 平均: {stats['object_count_avg']:.0f}, 最大: {stats['object_count_max']}, 最小: {stats['object_count_min']}")
            print(f"内存稳定性: {'✅ 稳定' if stats['memory_stable'] else '⚠️ 可能有内存泄漏'}")
            print(f"检查次数: {stats['checks']}")
            print("====================\n")

def simulate_user_interactions(app):
    """模拟用户交互"""
    interactions = [
        ("等待初始化", 2),
        ("切换激励语句", lambda: app.change_motto() if hasattr(app, 'change_motto') else None),
        ("等待", 3),
        ("再次切换激励语句", lambda: app.change_motto() if hasattr(app, 'change_motto') else None),
        ("等待", 2),
    ]
    
    for action, param in interactions:
        if callable(param):
            print(f"执行: {action}")
            try:
                param()
            except Exception as e:
                print(f"操作失败: {e}")
        else:
            print(f"执行: {action} ({param}秒)")
            time.sleep(param)

def run_simple_performance_test():
    """运行简化性能测试"""
    print("=== 高考倒计时软件简化性能测试 ===\n")
    print("测试说明:")
    print("- 不需要额外依赖库")
    print("- 测试应用内部性能指标")
    print("- 监控内存稳定性")
    print("- 模拟用户操作场景")
    print("\n开始测试...\n")
    
    # 创建监控器
    monitor = SimplePerformanceMonitor()
    monitor.start_monitoring()
    
    try:
        # 创建应用
        app = CountdownTimer()
        
        # 自动测试线程
        def auto_test():
            print("开始模拟用户操作...")
            
            # 模拟用户交互
            simulate_user_interactions(app)
            
            print("\n用户操作完成，继续运行5秒观察性能...")
            time.sleep(5)
            
            print("\n测试完成！正在收集数据...")
            
            # 获取应用性能统计
            app_stats = app.get_performance_stats()
            if app_stats:
                print("\n=== 应用性能报告 ===")
                print(f"运行时间: {app_stats['runtime']:.1f}秒")
                print(f"总更新次数: {app_stats['total_updates']}")
                print(f"UI更新次数: {app_stats['ui_updates']}")
                print(f"更新频率: {app_stats['update_rate']:.1f}次/秒")
                print(f"UI更新频率: {app_stats['ui_update_rate']:.1f}次/秒")
                print(f"更新效率: {app_stats['efficiency']:.1f}%")
                
                # 性能评级
                if app_stats['efficiency'] < 15:
                    grade = "🏆 优秀"
                    desc = "性能优化效果显著"
                elif app_stats['efficiency'] < 30:
                    grade = "✅ 良好"
                    desc = "性能有明显改善"
                elif app_stats['efficiency'] < 60:
                    grade = "⚠️ 一般"
                    desc = "还有优化空间"
                else:
                    grade = "❌ 需改进"
                    desc = "建议进一步优化"
                
                print(f"性能评级: {grade} - {desc}")
                print("=====================")
            
            # 停止监控
            monitor.stop_monitoring()
            time.sleep(1)
            monitor.print_stats()
            
            # 优化效果分析
            print("=== 优化效果分析 ===")
            if app_stats:
                saved_updates = app_stats['total_updates'] - app_stats['ui_updates']
                print(f"节省的UI更新次数: {saved_updates}")
                print(f"性能提升估算: {app_stats['efficiency']:.1f}% → 优化前100%")
                print(f"CPU使用率预计降低: {100 - app_stats['efficiency']:.1f}%")
            print("===================\n")
            
            print("测试完成！按 Ctrl+C 或关闭窗口退出。")
            
            # 5秒后自动退出
            time.sleep(5)
            app.root.quit()
        
        # 启动测试线程
        test_thread = threading.Thread(target=auto_test, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        monitor.stop_monitoring()
        monitor.print_stats()
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
        monitor.stop_monitoring()

def show_test_info():
    """显示测试信息"""
    print("📊 性能测试指标说明:")
    print("• 更新效率: UI更新次数 / 总更新次数 * 100%")
    print("• 效率越低越好（说明避免了更多无效更新）")
    print("• 理想效率: < 15%（优秀）")
    print("• 内存稳定性: 对象数量变化幅度")
    print()

if __name__ == "__main__":
    show_test_info()
    run_simple_performance_test()
