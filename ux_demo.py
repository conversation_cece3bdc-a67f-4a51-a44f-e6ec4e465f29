#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验演示脚本
展示高考倒计时软件的用户体验优化功能
"""

import time
import threading
from gaokao import CountdownTimer

def run_ux_demo():
    """运行用户体验演示"""
    print("🎉 高考倒计时软件用户体验演示")
    print("=" * 50)
    print("🌟 欢迎体验全新的用户界面！")
    print()
    print("📋 本次演示将展示:")
    print("  • 🎹 键盘快捷键操作")
    print("  • 🎨 主题切换效果")
    print("  • 💬 智能反馈系统")
    print("  • 💡 工具提示功能")
    print("  • 🎯 流畅的交互体验")
    print()
    print("💡 提示: 演示过程中您可以:")
    print("  • 按 F1 查看完整快捷键帮助")
    print("  • 右键打开设置面板")
    print("  • 尝试各种快捷键操作")
    print("  • 鼠标悬停查看工具提示")
    print()
    
    try:
        app = CountdownTimer()
        
        def demo_sequence():
            print("⏳ 应用启动中，请稍候...")
            time.sleep(3)
            
            print("\n🎬 开始用户体验演示...")
            
            # 演示1: 主题切换
            print("\n🎨 演示1: 主题切换系统")
            print("   展示三种精美主题...")
            
            themes = [
                ('default', '默认主题 - 清新蓝色'),
                ('dark', '深色主题 - 护眼深色'),
                ('colorful', '彩色主题 - 活泼多彩')
            ]
            
            for theme, desc in themes:
                print(f"   切换到 {desc}")
                app.switch_theme(theme)
                time.sleep(2.5)
            
            # 演示2: 快捷键操作
            print("\n🎹 演示2: 键盘快捷键")
            print("   展示便捷的快捷键操作...")
            
            shortcuts = [
                ('Ctrl+M', '切换激励语句', lambda: app.change_motto()),
                ('空格键', '切换目标', lambda: app.next_target()),
                ('Ctrl+T', '切换置顶', lambda: app.toggle_topmost()),
                ('F5', '刷新显示', lambda: app.refresh_display()),
            ]
            
            for key, desc, action in shortcuts:
                print(f"   {key}: {desc}")
                action()
                time.sleep(2)
            
            # 演示3: 反馈系统
            print("\n💬 演示3: 智能反馈系统")
            print("   展示实时操作反馈...")
            
            feedback_demos = [
                "演示反馈效果",
                "主题切换完成",
                "功能开关状态",
                "操作确认提示"
            ]
            
            for feedback in feedback_demos:
                print(f"   显示反馈: {feedback}")
                app.show_switch_feedback(feedback)
                time.sleep(3)
            
            # 演示4: 目标切换
            if len(app.config.get('targets', [])) > 1:
                print("\n🎯 演示4: 目标切换功能")
                print("   展示多种切换方式...")
                
                print("   方向键切换...")
                app.next_target()
                time.sleep(2)
                app.previous_target()
                time.sleep(2)
                
                print("   数字键快速定位...")
                app.switch_to_target_by_key(0)
                time.sleep(2)
            
            # 演示总结
            print("\n🏆 演示完成！用户体验亮点总结:")
            print("   ✨ 丰富的快捷键支持 - 提升操作效率")
            print("   🎨 精美的主题系统 - 个性化体验")
            print("   💬 智能的反馈提示 - 操作确认清晰")
            print("   💡 贴心的工具提示 - 新手友好")
            print("   ⚡ 流畅的交互响应 - 使用愉悦")
            
            print("\n🎮 现在您可以自由体验:")
            print("   • 尝试各种快捷键组合")
            print("   • 切换不同主题风格")
            print("   • 鼠标悬停查看提示")
            print("   • 右键打开设置面板")
            print("   • 按 F1 查看帮助信息")
            
            print("\n💝 感谢体验高考倒计时软件！")
            print("   祝您学习进步，金榜题名！🎓")
            
            # 继续运行让用户自由体验
            print("\n⏰ 应用将继续运行，您可以自由体验各项功能...")
            print("   按 Ctrl+Q 或关闭窗口退出")
        
        # 启动演示
        demo_thread = threading.Thread(target=demo_sequence, daemon=True)
        demo_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"演示出错: {e}")
        import traceback
        traceback.print_exc()

def show_welcome_message():
    """显示欢迎信息"""
    print("🎓 高考倒计时软件 v2.0")
    print("   专业的学习时间管理工具")
    print()
    print("🚀 全新用户体验特性:")
    print("   • 🎹 专业级快捷键支持")
    print("   • 🎨 多主题视觉系统")
    print("   • 💬 智能操作反馈")
    print("   • 💡 贴心工具提示")
    print("   • ⚡ 流畅交互体验")
    print()

if __name__ == "__main__":
    show_welcome_message()
    run_ux_demo()
