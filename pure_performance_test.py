#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯性能测试脚本
测试应用在正常运行时的性能（不包括用户操作）
"""

import time
import threading
from gaokao import CountdownTimer

def run_pure_performance_test():
    """运行纯性能测试"""
    print("=== 高考倒计时软件纯性能测试 ===\n")
    print("测试说明:")
    print("- 测试应用正常运行时的性能")
    print("- 不包括用户手动操作")
    print("- 重点关注UI更新优化效果")
    print("- 运行15秒进行测量")
    print("\n开始测试...\n")
    
    try:
        # 创建应用
        app = CountdownTimer()
        
        # 自动测试线程
        def auto_test():
            print("应用启动完成，开始性能监控...")
            
            # 等待3秒让应用稳定运行
            time.sleep(3)
            
            # 记录开始时的统计
            start_stats = app.get_performance_stats()
            if start_stats:
                start_updates = start_stats['total_updates']
                start_ui_updates = start_stats['ui_updates']
                print(f"监控开始 - 总更新: {start_updates}, UI更新: {start_ui_updates}")
            
            # 监控10秒的纯运行性能
            print("开始10秒纯运行性能监控...")
            time.sleep(10)
            
            # 记录结束时的统计
            end_stats = app.get_performance_stats()
            if end_stats and start_stats:
                period_total_updates = end_stats['total_updates'] - start_updates
                period_ui_updates = end_stats['ui_updates'] - start_ui_updates
                
                print(f"监控结束 - 总更新: {end_stats['total_updates']}, UI更新: {end_stats['ui_updates']}")
                print(f"监控期间 - 总更新: {period_total_updates}, UI更新: {period_ui_updates}")
                
                if period_total_updates > 0:
                    efficiency = (period_ui_updates / period_total_updates) * 100
                    print(f"\n=== 纯运行性能报告 ===")
                    print(f"监控时长: 10秒")
                    print(f"期间总更新次数: {period_total_updates}")
                    print(f"期间UI更新次数: {period_ui_updates}")
                    print(f"更新频率: {period_total_updates / 10:.1f}次/秒")
                    print(f"UI更新频率: {period_ui_updates / 10:.1f}次/秒")
                    print(f"更新效率: {efficiency:.1f}%")
                    
                    # 性能评级
                    if efficiency < 10:
                        grade = "🏆 优秀"
                        desc = "性能优化效果显著，几乎没有无效更新"
                    elif efficiency < 20:
                        grade = "✅ 良好"
                        desc = "性能优化效果明显"
                    elif efficiency < 50:
                        grade = "⚠️ 一般"
                        desc = "有一定优化效果，但还有改进空间"
                    else:
                        grade = "❌ 需改进"
                        desc = "优化效果不明显，建议检查代码"
                    
                    print(f"性能评级: {grade}")
                    print(f"评价: {desc}")
                    print("========================")
                    
                    # 理论对比
                    print("\n=== 与优化前对比 ===")
                    theoretical_old_updates = period_total_updates  # 优化前每次都更新UI
                    saved_updates = theoretical_old_updates - period_ui_updates
                    improvement = (saved_updates / theoretical_old_updates * 100) if theoretical_old_updates > 0 else 0
                    
                    print(f"理论优化前UI更新次数: {theoretical_old_updates}")
                    print(f"实际UI更新次数: {period_ui_updates}")
                    print(f"节省的UI更新次数: {saved_updates}")
                    print(f"性能提升: {improvement:.1f}%")
                    print(f"预计CPU使用率降低: {improvement * 0.8:.1f}%")
                    print("====================")
                    
                    # 具体分析
                    print("\n=== 详细分析 ===")
                    if period_ui_updates == 0:
                        print("✅ 完美！监控期间没有任何UI更新")
                        print("   说明缓存机制工作正常，避免了所有无效更新")
                    elif period_ui_updates <= 2:
                        print("✅ 优秀！监控期间UI更新次数很少")
                        print("   说明只在必要时才更新UI")
                    elif period_ui_updates <= 5:
                        print("✅ 良好！UI更新次数控制得当")
                    else:
                        print("⚠️ 注意：UI更新次数较多")
                        print("   可能需要进一步优化缓存策略")
                    
                    print(f"平均每秒UI更新: {period_ui_updates / 10:.1f}次")
                    if period_ui_updates / 10 < 0.5:
                        print("✅ 非常节能的更新频率")
                    elif period_ui_updates / 10 < 1:
                        print("✅ 合理的更新频率")
                    else:
                        print("⚠️ 更新频率偏高")
                    print("================")
            
            print("\n测试完成！按 Ctrl+C 或关闭窗口退出。")
            
            # 2秒后自动退出
            time.sleep(2)
            app.root.quit()
        
        # 启动测试线程
        test_thread = threading.Thread(target=auto_test, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

def show_optimization_explanation():
    """显示优化原理说明"""
    print("🔧 性能优化原理:")
    print("1. 缓存机制: 记录上次的值，只在变化时更新")
    print("2. 智能检测: 比较新旧值，避免无效的UI操作")
    print("3. 批量更新: 将多个UI更新合并到一个周期")
    print("4. 异步网络: 网络请求不阻塞UI线程")
    print()
    print("📊 理想性能指标:")
    print("• 更新效率: < 10% (优秀)")
    print("• UI更新频率: < 0.5次/秒 (节能)")
    print("• 内存稳定: 无明显增长")
    print()

if __name__ == "__main__":
    show_optimization_explanation()
    run_pure_performance_test()
