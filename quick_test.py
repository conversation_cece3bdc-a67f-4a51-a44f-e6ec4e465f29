#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证性能优化修复
"""

import time
import threading
from gaokao import CountdownTimer

def quick_test():
    """快速测试性能优化效果"""
    print("=== 快速性能测试 ===")
    print("测试时长: 8秒")
    print("目标: 验证UI更新计数修复")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_logic():
            print("⏳ 等待应用初始化...")
            time.sleep(2)
            
            # 记录开始状态
            start_stats = app.get_performance_stats()
            print(f"📊 开始 - 总更新: {start_stats['total_updates']}, UI更新: {start_stats['ui_updates']}")
            
            # 运行5秒
            print("🔍 监控5秒...")
            time.sleep(5)
            
            # 记录结束状态
            end_stats = app.get_performance_stats()
            
            # 计算期间的更新
            period_total = end_stats['total_updates'] - start_stats['total_updates']
            period_ui = end_stats['ui_updates'] - start_stats['ui_updates']
            
            print(f"📊 结束 - 总更新: {end_stats['total_updates']}, UI更新: {end_stats['ui_updates']}")
            print(f"📈 期间 - 总更新: {period_total}, UI更新: {period_ui}")
            
            if period_total > 0:
                efficiency = (period_ui / period_total) * 100
                print(f"⚡ 更新效率: {efficiency:.1f}%")
                
                if period_ui <= period_total:
                    if efficiency < 20:
                        print("✅ 优秀！UI更新效率很高")
                    elif efficiency < 50:
                        print("👍 良好！UI更新效率合理")
                    else:
                        print("⚠️ 一般：还有优化空间")
                else:
                    print("❌ 错误：UI更新次数不应该超过总更新次数")
            
            print("\n✅ 测试完成")
            app.root.quit()
        
        # 启动测试
        test_thread = threading.Thread(target=test_logic, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")

if __name__ == "__main__":
    quick_test()
