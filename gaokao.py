import tkinter as tk
from tkinter import ttk
import datetime
import json
import os
import random
import requests
from tkinter import messagebox
import threading
import time
import sys

class CountdownTimer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # 先隐藏窗口
        self.root.title("高考倒计时软件")

        # 性能优化：缓存变量
        self._last_update_time = 0
        self._last_days = None
        self._last_hours = None
        self._last_minutes = None
        self._last_seconds = None
        self._cached_motto = None
        self._motto_cache_time = 0
        self._motto_cache_duration = 300  # 5分钟缓存
        self._is_updating = False

        # 异步网络请求相关
        self._motto_thread = None
        self._pending_motto = None

        # 性能监控
        self._update_count = 0
        self._ui_update_count = 0
        self._start_time = time.time()

        # 设置图标
        try:
            icon_path = os.environ.get('GAOKAO_RESOURCE_PATH', '')
            if icon_path:
                icon_file = os.path.join(icon_path, "time.ico")
                if os.path.exists(icon_file):
                    self.root.iconbitmap(icon_file)
        except Exception as e:
            print(f"设置图标时出错: {e}")
        self.root.attributes('-transparentcolor', 'white')
        self.root.attributes('-alpha', 0.8)
        self.root.overrideredirect(True)
        
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 加载配置
        self.config = self.load_config()
        
        # 计算窗口位置，确保不超出屏幕边界
        window_width = 300
        window_height = 240
        x = min(max(self.config['position'][0], 0), screen_width - window_width)
        y = min(max(self.config['position'][1], 0), screen_height - window_height)
        
        # 设置窗口大小和位置
        self.root.geometry(f'{window_width}x{window_height}+{x}+{y}')
        self.root.configure(bg='white')
        
        # 保存窗口位置用的变量
        self.x = 0
        self.y = 0
        
        # 当前显示的目标索引
        self.current_target_index = 0
        
        # 主框架
        self.main_frame = tk.Frame(
            self.root,
            bg='white',
            highlightthickness=0,
            borderwidth=0,
            width=300,
            height=240
        )
        self.main_frame.propagate(False)  # 固定尺寸提升性能
        self.main_frame.pack_propagate(0) 
        self.main_frame.pack(padx=5, pady=5)
        
        # 添加透明事件捕获层
        self.event_canvas = tk.Canvas(
            self.root,
            bg='#FEFFFF',
            highlightthickness=0,
            width=300,
            height=240
        )
        self.event_canvas.propagate(False)  # 固定尺寸提升性能
        self.event_canvas.pack_propagate(0) 
        self.event_canvas.pack(padx=10, pady=5)
        
        # 将原有main_frame提升到canvas上层
        self.event_canvas.create_window(0, 0, 
                                    window=self.main_frame, 
                                    anchor='nw')
        
        # 标题显示（第一行）
        self.title_label = tk.Label(
            self.event_canvas,
            text="",  # 将在update_countdown中设置
            font=("微软雅黑", 14),
            fg='#4169E1',
            bg='#FEFFFF'
        )
        self.title_label.pack(padx=0, pady=0)
        
        # 剩余天数显示（第二行）
        self.days_label = tk.Label(
            self.event_canvas,
            text="",
            font=("微软雅黑", 30, "bold"),
            fg='#FF4500',
            bg='#FEFFFF'
        )
        self.days_label.pack()
        
        # 倒计时显示（第三行）
        self.countdown_label = tk.Label(
            self.event_canvas,
            text="",
            font=("微软雅黑", 16, ),
            fg='#758796',
            bg='#FEFFFF'
        )
        self.countdown_label.pack()
        
        # 日期显示（第四行）
        self.date_label = tk.Label(
            self.event_canvas,
            text="",
            font=("微软雅黑", 12),
            fg='#696969',
            bg='#FEFFFF'
        )
        self.date_label.pack()
        
        # 默认激励语句列表
        self.default_mottos = [
            "不明白你们遇到好事，为什么要掐腿揉眼睛，真醒了怎么办？",
            "天道酬勤，未来可期。",
            "一分耕耘，一分收获。",
            "坚持就是胜利。",
            "相信自己，你就是最好的。",
            "付出终有回报，努力不会白费。"
        ]
        
        # 当前使用的激励语句
        self.current_motto = self.get_motto()
        
        # 激励文字
        self.motto_label = tk.Label(
            self.event_canvas,
            text=self.current_motto,
            font=("微软雅黑", 10),
            fg='#77C5E6',
            bg='#FEFFFF',
            wraplength=260,  # 设置较小的换行宽度以确保两行显示
            height=3  # 设置固定高度为2行
        )
        self.motto_label.pack()
        
        # 添加切换按钮框架
        self.button_frame = tk.Frame(self.event_canvas, bg='#FEFFFF')
        self.button_frame.pack(pady=5)
        
        # 添加目标指示器框架
        self.dots_frame = tk.Frame(self.button_frame, bg='#FEFFFF')
        self.dots_frame.pack()
        
        # 存储圆点标签的列表
        self.dot_labels = []
        
        # 控制面板
        self.control_window = None
        
        # 绑定鼠标事件
        self.root.bind('<Button-1>', self.save_last_click)
        self.root.bind('<B1-Motion>', self.drag_window)
        self.root.bind('<Button-3>', self.show_control_panel)
        
        # 设置圆角窗口
        self.set_rounded_corners()
        
        # 开始倒计时
        self.update_countdown()

    def set_rounded_corners(self):
        """设置窗口圆角"""
        import win32gui
        import win32con
        #import win32api

        # 等待窗口完全加载
        self.root.update_idletasks()

        def async_draw():
            try:
                hwnd = self.root.winfo_id()
                width = self.root.winfo_width()
                height = self.root.winfo_height()
                
                # 创建圆角区域
                radius = 20
                region = win32gui.CreateRoundRectRgn(0, 0, width+1, height+1, radius, radius)
                win32gui.SetWindowRgn(hwnd, region, True)
                
                # 设置窗口样式，移除WS_EX_TRANSPARENT以防止点击穿透
                current_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
                # 确保窗口只有WS_EX_LAYERED样式，移除WS_EX_TRANSPARENT
                new_style = (current_style | win32con.WS_EX_LAYERED) & ~0x00000020
                win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, new_style)
                
                # 设置窗口透明通道
                win32gui.SetLayeredWindowAttributes(hwnd, 0, 255, 
                                                win32con.LWA_COLORKEY | win32con.LWA_ALPHA)
                
                # 显示窗口
                self.root.deiconify()
            except Exception as e:
                print(f"设置圆角窗口时出错: {e}")
        
        # 延迟执行确保窗口已渲染
        self.root.after(10, async_draw)

    # 修复所有方法的缩进，确保它们都是 CountdownTimer 类的直接方法
    def load_config(self):
        default_config = {
            'transparency': 0.8,
            'targets': [
                {
                    'name': '高考',
                    'date': '2025-06-07'
                }
            ],
            'position': [100, 100],
            'topmost': False,
            'auto_start': False
        }
        
        # 获取配置文件保存目录
        config_dir = os.environ.get('GAOKAO_CONFIG_DIR', os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(config_dir, 'gaokao_config.json')

        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if not all(key in config for key in default_config.keys()):
                        return default_config
                    return config
            return default_config
        except:
            return default_config

    def save_config(self):
        # 获取配置文件保存目录
        config_dir = os.environ.get('GAOKAO_CONFIG_DIR', os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(config_dir, 'gaokao_config.json')
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)

    def save_last_click(self, event):
        self.x = event.x
        self.y = event.y

    def drag_window(self, event):
        new_x = self.root.winfo_x() + (event.x - self.x)
        new_y = self.root.winfo_y() + (event.y - self.y)
        self.root.geometry(f"+{new_x}+{new_y}")
        self.config['position'] = [new_x, new_y]
        self.save_config()

    def show_control_panel(self, event):
        if self.control_window is None or not tk.Toplevel.winfo_exists(self.control_window):
            # 获取屏幕宽度和高度
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # 计算控制面板的位置
            main_x = self.root.winfo_x()
            main_y = self.root.winfo_y()
            main_width = self.root.winfo_width()
            
            # 设置控制面板在主窗口右侧，但不超出屏幕
            panel_width = 300  # 控制面板宽度
            panel_x = min(main_x + main_width + 10, screen_width - panel_width)  # 确保不超出右边界
            panel_y = min(main_y, screen_height - 520)  # 确保不超出下边界
            
            # 如果右侧空间不足，则显示在左侧
            if panel_x + panel_width > screen_width:
                panel_x = max(0, main_x - panel_width - 10)
            
            # 创建窗口时直接设置位置
            self.control_window = tk.Toplevel(self.root)
            self.control_window.withdraw()  # 先隐藏窗口
            self.control_window.title("设置")
            #self.control_window.iconbitmap('time.ico') #需要提供的图标
            self.control_window.geometry(f'300x520+{panel_x}+{panel_y}')  # 直接设置大小和位置
            self.control_window.resizable(False, False)
            self.control_window.deiconify()  # 显示窗口
            
            # 创建主框架并添加内边距
            main_frame = tk.Frame(self.control_window, padx=20, pady=10)
            main_frame.pack(fill='both', expand=True)

            # 透明度控制区域
            transparency_frame = tk.LabelFrame(main_frame, text="透明度设置", padx=10, pady=5)
            transparency_frame.pack(fill='x', pady=(0, 10))

            transparency = ttk.Scale(
                transparency_frame,
                from_=0.1,
                to=1.0,
                value=self.config['transparency'],
                command=self.update_transparency
            )
            transparency.pack(fill='x', pady=5)

            # 目标管理区域
            targets_frame = tk.LabelFrame(main_frame, text="倒计时目标管理", padx=10, pady=5)
            targets_frame.pack(fill='both', expand=True)

            # 目标列表框架
            self.target_frame = tk.Frame(targets_frame)
            self.target_frame.pack(fill='both', expand=True, pady=5)

            # 添加目标按钮
            add_button = tk.Button(
                targets_frame,
                text="添加新目标",
                command=self.add_target,
                width=15
            )
            add_button.pack(pady=10)

            # 添加窗口置顶和开机启动选项
            options_frame = tk.LabelFrame(main_frame, text="其他设置", padx=10, pady=5)
            options_frame.pack(fill='x', pady=(0, 10))

            # 窗口置顶选项
            self.topmost_var = tk.BooleanVar(value=self.config.get('topmost', False))
            topmost_check = tk.Checkbutton(
                options_frame,
                text="窗口置顶",
                variable=self.topmost_var,
                command=self.toggle_topmost
            )
            topmost_check.pack(anchor='w')

            # 开机启动选项
            self.auto_start_var = tk.BooleanVar(value=self.config.get('auto_start', False))
            auto_start_check = tk.Checkbutton(
                options_frame,
                text="开机启动",
                variable=self.auto_start_var,
                command=self.toggle_auto_start
            )
            auto_start_check.pack(anchor='w')
            
            # API开关选项
            self.use_api_var = tk.BooleanVar(value=self.config.get('use_api', False))
            use_api_check = tk.Checkbutton(
                options_frame,
                text="使用在线激励语句",
                variable=self.use_api_var,
                command=self.toggle_use_api
            )
            use_api_check.pack(anchor='w')
            # 添加切换激励语句和关闭程序按钮的框架
            buttons_frame = tk.Frame(main_frame)
            buttons_frame.pack(fill='x', pady=10)

            # 添加切换激励语句按钮（靠左）
            change_motto_button = tk.Button(
                buttons_frame,
                text="切换激励语句",
                command=self.change_motto,
                width=12
            )
            change_motto_button.pack(side='left', padx=2)

            # 添加性能统计按钮（中间）
            stats_button = tk.Button(
                buttons_frame,
                text="性能统计",
                command=self.print_performance_stats,
                width=10
            )
            stats_button.pack(side='left', padx=2)

            # 添加关闭程序按钮（靠右）
            close_button = tk.Button(
                buttons_frame,
                text="关闭程序",
                command=self.confirm_exit,
                fg='red',
                width=12
            )
            close_button.pack(side='right', padx=2)

            self.update_target_list()
    def update_transparency(self, value):
        transparency = float(value)
        self.root.attributes('-alpha', transparency)
        self.config['transparency'] = transparency
        self.save_config()

    def add_target(self):
        # 检查目标数量是否已达到上限
        if len(self.config['targets']) >= 5:
            tk.messagebox.showwarning("提示", "最多只能添加5个倒计时目标")
            return
        
        # 获取屏幕宽度和高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算控制面板的位置
        main_x = self.root.winfo_x()
        main_y = self.root.winfo_y()
        main_width = self.root.winfo_width()
        
        # 设置控制面板在主窗口右侧，但不超出屏幕
        panel_width = 300  # 控制面板宽度
        panel_x = min(main_x + main_width + 10, screen_width - panel_width)  # 确保不超出右边界
        panel_y = min(main_y, screen_height - 250)  # 确保不超出下边界
        
        # 如果右侧空间不足，则显示在左侧
        if panel_x + panel_width > screen_width:
            panel_x = max(0, main_x - panel_width - 10)

        dialog = tk.Toplevel(self.control_window)
        dialog.withdraw()  # 先隐藏窗口
        dialog.title("添加目标")
        #dialog.iconbitmap('time.ico') #需要提供的图标
        dialog.geometry('300x250')
        dialog.resizable(False, False)

        # 设置控制面板位置
        dialog.geometry(f"+{panel_x}+{panel_y}")
        dialog.deiconify()  # 显示窗口

        # 创建主框架
        main_frame = tk.Frame(dialog, padx=20, pady=10)
        main_frame.pack(fill='both', expand=True)

        # 名称输入区域
        name_frame = tk.Frame(main_frame)
        name_frame.pack(fill='x', pady=(0, 10))
        tk.Label(name_frame, text="目标名称:", width=10, anchor='w').pack(side='left')
        name_entry = tk.Entry(name_frame)
        name_entry.pack(side='left', fill='x', expand=True)

        # 日期输入区域
        date_frame = tk.Frame(main_frame)
        date_frame.pack(fill='x', pady=(0, 10))
        tk.Label(date_frame, text="目标日期:", width=10, anchor='w').pack(side='left')

        # 年份输入
        year_var = tk.StringVar(value=str(datetime.datetime.now().year))
        year_spinbox = ttk.Spinbox(date_frame, from_=2024, to=2100, width=5, textvariable=year_var)
        year_spinbox.pack(side='left')
        tk.Label(date_frame, text="年").pack(side='left', padx=2)

        # 月份输入
        month_var = tk.StringVar(value='01')
        month_spinbox = ttk.Spinbox(date_frame, from_=1, to=12, width=3, textvariable=month_var, format='%02.0f')
        month_spinbox.pack(side='left')
        tk.Label(date_frame, text="月").pack(side='left', padx=2)

        # 日期输入
        day_var = tk.StringVar(value='01')
        day_spinbox = ttk.Spinbox(date_frame, from_=1, to=31, width=3, textvariable=day_var, format='%02.0f')
        day_spinbox.pack(side='left')
        tk.Label(date_frame, text="日").pack(side='left', padx=2)

        # 时间输入区域
        time_frame = tk.Frame(main_frame)
        time_frame.pack(fill='x', pady=(0, 20))
        tk.Label(time_frame, text="目标时间:", width=10, anchor='w').pack(side='left')

        # 小时输入
        hour_var = tk.StringVar(value='00')
        hour_spinbox = ttk.Spinbox(time_frame, from_=0, to=23, width=3, textvariable=hour_var, format='%02.0f')
        hour_spinbox.pack(side='left')
        tk.Label(time_frame, text=":").pack(side='left', padx=2)

        # 分钟输入
        minute_var = tk.StringVar(value='00')
        minute_spinbox = ttk.Spinbox(time_frame, from_=0, to=59, width=3, textvariable=minute_var, format='%02.0f')
        minute_spinbox.pack(side='left')

        # 按钮区域
        button_frame = tk.Frame(main_frame)
        button_frame.pack(side='bottom', pady=(0, 10))

        def save():
            name = name_entry.get().strip()
            year = year_var.get().zfill(4)
            month = month_var.get().zfill(2)
            day = day_var.get().zfill(2)
            hour = hour_var.get().zfill(2)
            minute = minute_var.get().zfill(2)

            if not name:
                tk.messagebox.showwarning("提示", "请输入目标名称")
                return

            date = f"{year}-{month}-{day}"

            try:
                datetime.datetime.strptime(f"{date} {hour}:{minute}", '%Y-%m-%d %H:%M')
                self.config['targets'].append({
                    'name': name,
                    'date': date,
                    'time': f"{hour}:{minute}"
                })
                self.save_config()
                self.update_target_list()
                dialog.destroy()
            except ValueError:
                tk.messagebox.showerror("错误", "日期无效，请检查输入")

        # 取消按钮
        tk.Button(button_frame, text="取消", width=10, command=dialog.destroy).pack(side='left', padx=5)
        # 保存按钮
        tk.Button(button_frame, text="保存", width=10, command=save).pack(side='left', padx=5)

    def update_target_list(self):
        for widget in self.target_frame.winfo_children():
            widget.destroy()

        for i, target in enumerate(self.config['targets']):
            frame = tk.Frame(self.target_frame)
            frame.pack(fill='x', pady=2)

            time_str = target.get('time', '00:00')
            tk.Label(
                frame,
                text=f"{target['name']}: {target['date']} {time_str}",
                anchor='w'
            ).pack(side='left', fill='x', expand=True)

            tk.Button(
                frame,
                text="删除",
                command=lambda idx=i: self.delete_target(idx),
                width=6
            ).pack(side='right')

    def confirm_exit(self):
        if tk.messagebox.askokcancel("确认", "确定要关闭程序吗？"):
            self.root.quit()
            self.root.destroy()

    def delete_target(self, index):
        self.config['targets'].pop(index)
        # 如果删除的是当前显示的目标，调整当前索引
        if index == self.current_target_index:
            self.current_target_index = 0
        elif index < self.current_target_index:
            self.current_target_index -= 1

        self.save_config()
        self.update_target_list()
        self.update_countdown()

    def get_motto(self):
        """获取激励语句，根据配置决定是否使用API（优化版本）"""
        current_time = time.time()

        # 检查缓存是否有效
        if (self._cached_motto and
            current_time - self._motto_cache_time < self._motto_cache_duration):
            return self._cached_motto

        # 如果启用API且没有正在进行的请求
        if (self.config.get('use_api', False) and
            (self._motto_thread is None or not self._motto_thread.is_alive())):
            # 启动异步请求
            self._start_async_motto_request()

        # 返回缓存的激励语句或默认语句
        if self._cached_motto:
            return self._cached_motto
        else:
            motto = random.choice(self.config.get('mottos', self.default_mottos))
            self._cached_motto = motto
            self._motto_cache_time = current_time
            return motto

    def _start_async_motto_request(self):
        """启动异步激励语句请求"""
        def fetch_motto():
            try:
                response = requests.get('https://jkapi.com/api/one_yan?type=json', timeout=3)
                if response.status_code == 200:
                    data = response.json()
                    motto = data.get('content', '')
                    if motto:
                        # 使用线程安全的方式更新UI
                        self.root.after(0, self._update_motto_from_thread, motto)
            except Exception as e:
                print(f"获取在线激励语句失败: {e}")

        self._motto_thread = threading.Thread(target=fetch_motto, daemon=True)
        self._motto_thread.start()

    def _update_motto_from_thread(self, motto):
        """从线程安全地更新激励语句"""
        self._cached_motto = motto
        self._motto_cache_time = time.time()
        if hasattr(self, 'motto_label'):
            self.motto_label.config(text=motto)

    def update_countdown(self):
        # 性能监控
        self._update_count += 1

        # 防止重复更新
        if self._is_updating:
            self.root.after(1000, self.update_countdown)
            return

        self._is_updating = True

        try:
            now = datetime.datetime.now()
            current_timestamp = int(now.timestamp())

            # 性能优化：只在秒数变化时才更新
            if current_timestamp == self._last_update_time:
                self._is_updating = False
                self.root.after(100, self.update_countdown)  # 更频繁检查但不更新UI
                return

            self._last_update_time = current_timestamp

            # 检查是否有目标
            if not self.config['targets']:
                if self._last_days != "no_targets":  # 只在状态改变时更新
                    self.title_label.config(text="没有设置倒计时目标")
                    self.days_label.config(text="请右键添加目标")
                    self.countdown_label.config(text="")
                    self.date_label.config(text="")
                    self.update_count_label()
                    self._last_days = "no_targets"
                self._is_updating = False
                self.root.after(1000, self.update_countdown)
                return
        except Exception as e:
            print(f"更新倒计时时出错: {e}")
            self._is_updating = False
            # 即使出错也继续运行
            self.root.after(1000, self.update_countdown)
            return

        try:
            # 确保当前索引在有效范围内
            if self.current_target_index >= len(self.config['targets']):
                self.current_target_index = 0

            # 获取当前显示的目标
            target = self.config['targets'][self.current_target_index]
            target_name = target['name']
            target_date_str = target['date']
            target_time_str = target.get('time', '00:00')

            # 解析目标日期和时间
            target_datetime = datetime.datetime.strptime(
                f"{target_date_str} {target_time_str}",
                '%Y-%m-%d %H:%M'
            )

            # 计算剩余时间
            delta = target_datetime - now
            days = delta.days
            hours, remainder = divmod(delta.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)

            # 性能优化：只在数值变化时更新UI
            needs_update = False

            # 检查标题是否需要更新
            title_text = f"距离{target_name}还有"
            if not hasattr(self, '_last_title') or self._last_title != title_text:
                self.title_label.config(text=title_text)
                self._last_title = title_text
                needs_update = True
                self._ui_update_count += 1

            # 检查天数是否需要更新
            if self._last_days != days:
                if days > 0:
                    self.days_label.config(text=f"{days}天")
                else:
                    total_seconds = delta.total_seconds()
                    if total_seconds <= 0:
                        self.days_label.config(text="0天")
                    else:
                        self.days_label.config(text="<1天")
                self._last_days = days
                needs_update = True
                self._ui_update_count += 1

            # 检查时间是否需要更新
            if (self._last_hours != hours or self._last_minutes != minutes or
                self._last_seconds != seconds):

                total_seconds = delta.total_seconds()
                if total_seconds <= 0:
                    self.countdown_label.config(text="00:00:00")
                else:
                    self.countdown_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")

                self._last_hours = hours
                self._last_minutes = minutes
                self._last_seconds = seconds
                needs_update = True
                self._ui_update_count += 1

            # 只在天数变化时更新日期显示（减少计算）
            if needs_update or not hasattr(self, '_last_date_text'):
                total_seconds = delta.total_seconds()
                if total_seconds <= 0:
                    date_text = f"目标日期已到: {target_date_str}"
                else:
                    weekday = ['一', '二', '三', '四', '五', '六', '日'][target_datetime.weekday()]
                    date_text = f"目标日期: {target_date_str} 星期{weekday}"

                if not hasattr(self, '_last_date_text') or self._last_date_text != date_text:
                    self.date_label.config(text=date_text)
                    self._last_date_text = date_text

            # 只在需要时更新目标指示器
            if needs_update:
                self.update_count_label()

            # 只在配置变化时更新窗口置顶状态
            topmost_setting = self.config.get('topmost', False)
            if not hasattr(self, '_last_topmost') or self._last_topmost != topmost_setting:
                self.root.attributes('-topmost', topmost_setting)
                self._last_topmost = topmost_setting

        except Exception as e:
            print(f"更新倒计时详情时出错: {e}")
        finally:
            self._is_updating = False
            # 无论如何，1秒后再次更新
            self.root.after(1000, self.update_countdown)
    def update_count_label(self):
        """更新目标指示器显示（优化版本）"""
        total = len(self.config['targets'])

        # 性能优化：检查是否真的需要更新
        if (hasattr(self, '_last_total_targets') and
            hasattr(self, '_last_current_index') and
            self._last_total_targets == total and
            self._last_current_index == self.current_target_index):
            return  # 无需更新

        # 记录当前状态
        self._last_total_targets = total
        self._last_current_index = self.current_target_index

        # 如果只有一个目标或没有目标，隐藏所有圆点
        if total <= 1:
            for dot in self.dot_labels:
                dot.pack_forget()  # 使用pack_forget而不是destroy，提高性能
            return

        # 关键设置1：统一背景色（只在需要时设置）
        if not hasattr(self, '_dots_bg_set'):
            self.dots_frame.config(bg='white')
            self.button_frame.config(bg='white')
            self._dots_bg_set = True

        # 关键设置2：使用闭包工厂函数
        def create_handler(index):
            def handler(event):
                self.switch_to_target(index)
            return handler

        # 优化：重用现有的圆点，只在数量变化时重建
        current_dots = len(self.dot_labels)

        # 如果圆点数量不匹配，重建
        if current_dots != total:
            # 清除多余的圆点
            while len(self.dot_labels) > total:
                dot = self.dot_labels.pop()
                dot.destroy()

            # 添加不足的圆点
            while len(self.dot_labels) < total:
                i = len(self.dot_labels)
                dot = tk.Label(
                    self.dots_frame,
                    text="●",
                    font=("微软雅黑", 8),
                    bg='white',
                    cursor='hand2'
                )
                dot.pack(side='left', padx=2)
                dot.bind('<Button-1>', create_handler(i))
                self.dot_labels.append(dot)

        # 更新圆点颜色（只更新颜色，不重建）
        for i, dot in enumerate(self.dot_labels):
            color = '#4169E1' if i == self.current_target_index else '#D3D3D3'
            dot.config(fg=color)
            dot.pack(side='left', padx=2)  # 确保显示

        # 关键设置4：提升层级（只在首次设置时）
        if not hasattr(self, '_dots_lifted'):
            self.dots_frame.lift()
            self.dots_frame.bindtags((self.dots_frame, self.root, "all"))
            self._dots_lifted = True


    def switch_to_target(self, index):
        """切换到指定目标"""
        if self.config['targets']:
            self.current_target_index = index
            self.update_countdown()
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        is_topmost = self.topmost_var.get()
        self.root.attributes('-topmost', is_topmost)
        self.config['topmost'] = is_topmost
        self.save_config()

    def change_motto(self):
        """切换激励语句（优化版本）"""
        # 强制刷新缓存
        self._motto_cache_time = 0
        self._cached_motto = None

        new_motto = self.get_motto()
        if new_motto != self.current_motto:  # 只在真正变化时更新
            self.current_motto = new_motto
            self.motto_label.config(text=new_motto)

    def toggle_use_api(self):
        """切换是否使用API获取激励语句"""
        self.config['use_api'] = self.use_api_var.get()
        self.save_config()
        # 切换后立即更新激励语句
        self.change_motto()

    def get_performance_stats(self):
        """获取性能统计信息"""
        runtime = time.time() - self._start_time
        if runtime > 0:
            update_rate = self._update_count / runtime
            ui_update_rate = self._ui_update_count / runtime
            efficiency = (self._ui_update_count / self._update_count * 100) if self._update_count > 0 else 0

            return {
                'runtime': runtime,
                'total_updates': self._update_count,
                'ui_updates': self._ui_update_count,
                'update_rate': update_rate,
                'ui_update_rate': ui_update_rate,
                'efficiency': efficiency
            }
        return None

    def print_performance_stats(self):
        """打印性能统计信息（调试用）"""
        stats = self.get_performance_stats()
        if stats:
            print(f"=== 性能统计 ===")
            print(f"运行时间: {stats['runtime']:.1f}秒")
            print(f"总更新次数: {stats['total_updates']}")
            print(f"UI更新次数: {stats['ui_updates']}")
            print(f"更新频率: {stats['update_rate']:.1f}次/秒")
            print(f"UI更新频率: {stats['ui_update_rate']:.1f}次/秒")
            print(f"更新效率: {stats['efficiency']:.1f}% (UI更新/总更新)")
            print("==================")

    def toggle_auto_start(self):
        """切换开机启动状态"""
        import winreg
        key_path = r'Software\\Microsoft\\Windows\\CurrentVersion\\Run'
        app_path = os.path.abspath(sys.argv[0])

        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_ALL_ACCESS)
            if self.auto_start_var.get():
                winreg.SetValueEx(key, 'GaokaoCountdown', 0, winreg.REG_SZ, app_path)
            else:
                try:
                    winreg.DeleteValue(key, 'GaokaoCountdown')
                except WindowsError:
                    pass
            winreg.CloseKey(key)
            self.config['auto_start'] = self.auto_start_var.get()
            self.save_config()
        except Exception as e:
            messagebox.showerror("错误", f"设置开机启动失败：{str(e)}")
            self.auto_start_var.set(not self.auto_start_var.get())

    def run(self):
        # 设置初始位置
        if 'position' in self.config:
            self.root.geometry(f"+{self.config['position'][0]}+{self.config['position'][1]}")

        # 设置初始透明度
        self.root.attributes('-alpha', self.config['transparency'])

        # 设置窗口置顶状态
        if self.config.get('topmost', False):
            self.root.attributes('-topmost', True)
            
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("程序被用户中断，正在安全退出...")
            self.save_config()  # 保存配置
            self.root.quit()
            self.root.destroy()
        except Exception as e:
            print(f"程序发生错误: {e}，正在安全退出...")
            self.save_config()  # 保存配置
            self.root.quit()
            self.root.destroy()

if __name__ == "__main__":
    try:
        app = CountdownTimer()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
