#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能对比脚本
对比优化前后的性能差异
"""

import time
import threading
from gaokao import CountdownTimer

def simulate_usage(app, duration=10):
    """模拟用户使用场景"""
    start_time = time.time()
    
    def user_actions():
        """模拟用户操作"""
        time.sleep(2)
        
        # 模拟切换激励语句
        if hasattr(app, 'change_motto'):
            app.change_motto()
            
        time.sleep(3)
        
        # 模拟切换目标（如果有多个目标）
        if len(app.config.get('targets', [])) > 1:
            app.switch_to_target(1)
            
        time.sleep(2)
        
        # 再次切换激励语句
        if hasattr(app, 'change_motto'):
            app.change_motto()
    
    # 启动用户操作线程
    action_thread = threading.Thread(target=user_actions, daemon=True)
    action_thread.start()
    
    # 等待指定时间
    time.sleep(duration)
    
    return time.time() - start_time

def run_performance_comparison():
    """运行性能对比测试"""
    print("=== 高考倒计时软件性能对比测试 ===\n")
    
    print("正在启动优化版本测试...")
    print("测试将运行10秒，模拟正常使用场景\n")
    
    try:
        # 创建应用实例
        app = CountdownTimer()
        
        # 自动测试线程
        def auto_test():
            # 模拟使用
            runtime = simulate_usage(app, 10)
            
            print("测试完成！正在收集性能数据...\n")
            
            # 获取性能统计
            stats = app.get_performance_stats()
            if stats:
                print("=== 优化版本性能报告 ===")
                print(f"测试时间: {runtime:.1f}秒")
                print(f"总更新次数: {stats['total_updates']}")
                print(f"UI更新次数: {stats['ui_updates']}")
                print(f"更新频率: {stats['update_rate']:.1f}次/秒")
                print(f"UI更新频率: {stats['ui_update_rate']:.1f}次/秒")
                print(f"更新效率: {stats['efficiency']:.1f}%")
                print("========================\n")
                
                # 性能评估
                print("=== 性能评估 ===")
                if stats['efficiency'] < 20:
                    print("✅ 优秀：UI更新效率很高，性能优化效果显著")
                elif stats['efficiency'] < 50:
                    print("✅ 良好：UI更新效率较高，性能有明显改善")
                elif stats['efficiency'] < 80:
                    print("⚠️  一般：UI更新效率中等，还有优化空间")
                else:
                    print("❌ 需要改进：UI更新效率较低，建议进一步优化")
                    
                print(f"CPU友好度: {'高' if stats['update_rate'] < 2 else '中' if stats['update_rate'] < 5 else '低'}")
                print("================\n")
                
                # 与理论值对比
                print("=== 与优化前对比（理论值） ===")
                theoretical_old_ui_updates = stats['total_updates']  # 优化前每次都更新UI
                improvement = ((theoretical_old_ui_updates - stats['ui_updates']) / theoretical_old_ui_updates * 100) if theoretical_old_ui_updates > 0 else 0
                print(f"UI更新次数减少: {improvement:.1f}%")
                print(f"预计CPU使用率降低: {improvement * 0.8:.1f}%")
                print("============================\n")
            
            # 退出应用
            app.root.quit()
        
        # 启动测试线程
        test_thread = threading.Thread(target=auto_test, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

def show_optimization_tips():
    """显示优化建议"""
    print("=== 性能优化建议 ===")
    print("1. 🎯 UI更新优化:")
    print("   - 只在数值变化时更新界面")
    print("   - 缓存计算结果避免重复计算")
    print("   - 使用智能重绘策略")
    print()
    print("2. 🌐 网络请求优化:")
    print("   - 异步请求避免阻塞UI")
    print("   - 实现请求缓存机制")
    print("   - 设置合理的超时时间")
    print()
    print("3. 💾 内存优化:")
    print("   - 重用UI组件而非重建")
    print("   - 及时清理不需要的对象")
    print("   - 避免内存泄漏")
    print()
    print("4. 📊 性能监控:")
    print("   - 实时统计更新频率")
    print("   - 监控内存和CPU使用")
    print("   - 定期进行性能测试")
    print("===================\n")

if __name__ == "__main__":
    show_optimization_tips()
    run_performance_comparison()
