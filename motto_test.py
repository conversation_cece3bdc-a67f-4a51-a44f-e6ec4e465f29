#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激励语句切换测试脚本
测试在线激励语句的切换体验优化
"""

import time
import threading
from gaokao import CountdownTimer

def test_motto_switching():
    """测试激励语句切换功能"""
    print("🎯 激励语句切换测试")
    print("=" * 40)
    print("📋 测试内容:")
    print("  ✓ 本地激励语句切换")
    print("  ✓ 在线激励语句切换")
    print("  ✓ 网络失败时的降级处理")
    print("  ✓ 用户体验优化效果")
    print()
    
    try:
        app = CountdownTimer()
        
        def test_logic():
            print("⏳ 应用初始化中...")
            time.sleep(2)
            
            # 获取当前激励语句
            current_motto = app.current_motto
            print(f"📝 当前激励语句: {current_motto}")
            
            # 测试1：本地激励语句切换
            print(f"\n📊 测试1: 本地激励语句切换")
            print("   设置为使用本地激励语句...")
            app.config['use_api'] = False
            app.use_api_var.set(False)
            
            print("   执行切换...")
            app.change_motto()
            time.sleep(1)
            
            new_motto = app.current_motto
            print(f"   切换后: {new_motto}")
            
            if new_motto != current_motto:
                print("   ✅ 本地切换成功")
            else:
                print("   ⚠️ 本地切换可能失败（或随机到相同语句）")
            
            # 测试2：在线激励语句切换
            print(f"\n📊 测试2: 在线激励语句切换")
            print("   设置为使用在线激励语句...")
            app.config['use_api'] = True
            app.use_api_var.set(True)
            
            print("   执行切换...")
            before_switch = app.current_motto
            app.change_motto()
            
            # 检查是否显示加载提示
            loading_motto = app.current_motto
            print(f"   切换瞬间: {loading_motto}")
            
            if "正在获取" in loading_motto:
                print("   ✅ 显示加载提示，用户体验良好")
            else:
                print("   ⚠️ 未显示加载提示")
            
            # 等待异步请求完成
            print("   等待在线激励语句...")
            time.sleep(4)
            
            final_motto = app.current_motto
            print(f"   最终结果: {final_motto}")
            
            if "正在获取" not in final_motto:
                if final_motto != before_switch:
                    print("   ✅ 在线切换成功")
                else:
                    print("   ⚠️ 在线切换可能失败（或获取到相同内容）")
            else:
                print("   ❌ 在线切换失败，仍显示加载状态")
            
            # 测试3：多次快速切换
            print(f"\n📊 测试3: 快速连续切换")
            print("   执行3次快速切换...")
            
            for i in range(3):
                print(f"   第{i+1}次切换...")
                app.change_motto()
                time.sleep(1)
                print(f"   结果: {app.current_motto[:30]}...")
            
            print("   ✅ 快速切换测试完成")
            
            # 测试4：网络错误模拟（通过设置错误的API地址）
            print(f"\n📊 测试4: 网络错误处理")
            print("   注意：这个测试会故意触发网络错误")
            
            # 暂时修改API地址来模拟网络错误
            original_request = app._start_async_motto_request
            
            def mock_failed_request():
                def fetch_motto():
                    try:
                        # 模拟网络错误
                        raise Exception("模拟网络错误")
                    except Exception as e:
                        print(f"   模拟网络错误: {e}")
                        # 使用降级处理
                        import random
                        fallback_motto = random.choice(app.config.get('mottos', app.default_mottos))
                        app.root.after(0, app._update_motto_from_thread, fallback_motto)
                
                app._motto_thread = threading.Thread(target=fetch_motto, daemon=True)
                app._motto_thread.start()
            
            app._start_async_motto_request = mock_failed_request
            
            print("   执行网络错误切换...")
            app.change_motto()
            time.sleep(2)
            
            error_result = app.current_motto
            print(f"   错误处理结果: {error_result}")
            
            if "正在获取" not in error_result:
                print("   ✅ 网络错误降级处理成功")
            else:
                print("   ❌ 网络错误处理失败")
            
            # 恢复原始方法
            app._start_async_motto_request = original_request
            
            # 最终总结
            print(f"\n🏆 测试总结")
            print("=" * 40)
            print("✅ 优化效果:")
            print("  • 在线切换时显示加载提示")
            print("  • 网络失败时自动降级到本地语句")
            print("  • 避免了先显示本地再显示在线的问题")
            print("  • 提供了更好的用户反馈")
            
            print(f"\n💡 使用建议:")
            print("  • 启用在线激励语句获得更丰富内容")
            print("  • 网络不稳定时会自动使用本地语句")
            print("  • 加载提示让用户了解当前状态")
            
            print(f"\n✅ 测试完成！")
            app.root.quit()
        
        # 启动测试
        test_thread = threading.Thread(target=test_logic, daemon=True)
        test_thread.start()
        
        # 运行应用
        app.run()
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_motto_switching()
