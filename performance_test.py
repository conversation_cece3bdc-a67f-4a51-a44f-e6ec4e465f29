#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本
用于测试高考倒计时软件的性能优化效果
"""

import time
import threading
import psutil
import os
from gaokao import CountdownTimer

class PerformanceMonitor:
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.start_time = time.time()
        self.memory_samples = []
        self.cpu_samples = []
        self.monitoring = False
        
    def start_monitoring(self):
        """开始监控性能"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取内存使用情况
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                self.memory_samples.append(memory_mb)
                
                # 获取CPU使用率
                cpu_percent = self.process.cpu_percent()
                self.cpu_samples.append(cpu_percent)
                
                time.sleep(1)  # 每秒采样一次
            except Exception as e:
                print(f"监控出错: {e}")
                break
                
    def get_stats(self):
        """获取统计信息"""
        if not self.memory_samples or not self.cpu_samples:
            return None
            
        runtime = time.time() - self.start_time
        
        return {
            'runtime': runtime,
            'memory_avg': sum(self.memory_samples) / len(self.memory_samples),
            'memory_max': max(self.memory_samples),
            'memory_min': min(self.memory_samples),
            'cpu_avg': sum(self.cpu_samples) / len(self.cpu_samples),
            'cpu_max': max(self.cpu_samples),
            'samples': len(self.memory_samples)
        }
        
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        if stats:
            print("\n=== 系统性能统计 ===")
            print(f"运行时间: {stats['runtime']:.1f}秒")
            print(f"内存使用 - 平均: {stats['memory_avg']:.1f}MB, 最大: {stats['memory_max']:.1f}MB, 最小: {stats['memory_min']:.1f}MB")
            print(f"CPU使用 - 平均: {stats['cpu_avg']:.1f}%, 最大: {stats['cpu_max']:.1f}%")
            print(f"采样次数: {stats['samples']}")
            print("=====================\n")

def run_performance_test():
    """运行性能测试"""
    print("开始性能测试...")
    print("测试将运行30秒，请观察性能数据")
    
    # 创建性能监控器
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    try:
        # 创建倒计时应用
        app = CountdownTimer()
        
        # 运行30秒后自动停止
        def auto_stop():
            time.sleep(30)
            print("\n测试时间到，正在收集数据...")
            
            # 打印应用性能统计
            app.print_performance_stats()
            
            # 停止监控并打印系统性能
            monitor.stop_monitoring()
            time.sleep(1)  # 等待监控线程结束
            monitor.print_stats()
            
            # 退出应用
            app.root.quit()
            
        # 启动自动停止线程
        stop_thread = threading.Thread(target=auto_stop, daemon=True)
        stop_thread.start()
        
        # 运行应用
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        monitor.stop_monitoring()
        monitor.print_stats()
    except Exception as e:
        print(f"测试出错: {e}")
        monitor.stop_monitoring()

if __name__ == "__main__":
    # 检查依赖
    try:
        import psutil
    except ImportError:
        print("请先安装psutil: pip install psutil")
        exit(1)
        
    run_performance_test()
